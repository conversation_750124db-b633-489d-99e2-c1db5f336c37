<?php

namespace App\Endpoint\Web\DataGrid;

use App\Database\Entity\Message;
use App\Endpoint\Web\View\MessageView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Pagination\PagePaginator;
use Spiral\DataGrid\Specification\Sorter\AscSorter;
use Spiral\DataGrid\Specification\Sorter\Sorter;
use Spiral\DataGrid\Specification\Sorter\SorterSet;
use Spiral\DataGrid\Specification\Value\StringValue;
use Spiral\DataGrid\Specification\Value\UuidValue;

class MessageGridSchema extends GridSchema
{
    public function __construct(
        private readonly MessageView $messageView
    ) {
        $this->addFilter('conversation', new Equals('conversation.id', new UuidValue()));
        $this->addFilter('sender', new Equals('sender', new StringValue()));

        $this->addSorter('default', new SorterSet(
            new AscSorter('createdAt'),
        ));
        $this->addSorter('lastMessageTime',new Sorter('createdAt'));

        $this->setPaginator(new PagePaginator(50, [100, 200]));
    }

    public function __invoke(Message $message)
    {
        return $this->messageView->map($message);
    }
}
