<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Message;
use App\Endpoint\Web\DataGrid\MessageGridSchema;
use Cycle\ORM\Select;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class MessageController
{
    use PrototypeTrait;

    #[Route(route: '/message', name: 'message.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: MessageGridSchema::class)]
    public function list(): Select
    {
        return $this->messages->select()
            ->load('conversation');
    }

    #[Route(route: '/message/<message:uuid>', name: 'message.get', methods: 'GET', group: 'v1')]
    public function get(Message $message)
    {
        return $this->messageView->json($message);
    }
}
