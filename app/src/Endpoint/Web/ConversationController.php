<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Conversation;
use App\Endpoint\Web\DataGrid\ConversationGridSchema;
use Cycle\ORM\Select;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class ConversationController
{
    use PrototypeTrait;

    #[Route(route: '/conversation', name: 'conversation.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: ConversationGridSchema::class)]
    public function list(): Select
    {
        return $this->conversations->select()
            ->load('lead.person')
            ->load('agent')
            ->load('messages')
            ->load('calls');
    }

    #[Route(route: '/conversation/<conversation:uuid>', name: 'conversation.get', methods: 'GET', group: 'v1')]
    public function get(Conversation $conversation)
    {
        return $this->conversationView->json($conversation);
    }
}
