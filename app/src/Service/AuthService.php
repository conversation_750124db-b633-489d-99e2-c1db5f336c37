<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\User;
use App\Database\Repository\UserRepository;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Psr\Http\Message\ServerRequestInterface;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'authService')]
class AuthService
{
    private const JWT_SECRET = 'your-secret-key'; // TODO: Move to environment variable
    private const JWT_ALGORITHM = 'HS256';
    private const JWT_EXPIRATION = 3600 * 24 * 7; // 7 days
    private const COOKIE_NAME = 'bt_auth_token';

    public function __construct(
        private readonly UserRepository $userRepository
    ) {}

    /**
     * Authenticate user with username/email and password
     */
    public function authenticate(string $username, string $password): ?User
    {
        // Find user by username or email
        $user = $this->userRepository->findByUsernameOrEmail($username);

        if (!$user || !$user->password) {
            return null;
        }

        // Verify password
        if (!password_verify($password, $user->password)) {
            return null;
        }

        // Check if user is verified
        if (!$user->verified) {
            return null;
        }

        return $user;
    }

    /**
     * Generate JWT token for authenticated user
     */
    public function generateToken(User $user): string
    {
        $payload = [
            'iss' => 'back-talk-api', // Issuer
            'aud' => 'back-talk-app', // Audience
            'iat' => time(), // Issued at
            'exp' => time() + self::JWT_EXPIRATION, // Expiration
            'user_id' => $user->id->toString(),
            'username' => $user->username,
            'verified' => $user->verified,
            'organization_id' => $user->organization?->id?->toString(), // Include organization ID if exists
        ];

        return JWT::encode($payload, self::JWT_SECRET, self::JWT_ALGORITHM);
    }

    /**
     * Validate and decode JWT token
     */
    public function validateToken(string $token): ?array
    {
        try {
            $decoded = JWT::decode($token, new Key(self::JWT_SECRET, self::JWT_ALGORITHM));
            return (array) $decoded;
        } catch (ExpiredException $e) {
            // Token has expired
            return null;
        } catch (SignatureInvalidException $e) {
            // Token signature is invalid
            return null;
        } catch (\Exception $e) {
            // Other JWT errors
            return null;
        }
    }

    /**
     * Get user from JWT token
     */
    public function getUserFromToken(string $token): ?User
    {
        $payload = $this->validateToken($token);

        if (!$payload || !isset($payload['user_id'])) {
            return null;
        }

        return $this->userRepository->findByPK($payload['user_id']);
    }

    /**
     * Extract token from Authorization header
     */
    public function extractTokenFromHeader(string $authHeader): ?string
    {
        if (!str_starts_with($authHeader, 'Bearer ')) {
            return null;
        }

        return substr($authHeader, 7);
    }

    /**
     * Extract token from request cookies
     */
    public function extractTokenFromCookies(ServerRequestInterface $request): ?string
    {
        $cookies = $request->getCookieParams();
        return $cookies[self::COOKIE_NAME] ?? null;
    }

    /**
     * Extract token from request (tries cookies first, then Authorization header)
     */
    public function extractTokenFromRequest(ServerRequestInterface $request): ?string
    {
        // Try cookie first (more secure)
        $token = $this->extractTokenFromCookies($request);
        if ($token) {
            return $token;
        }

        // Fallback to Authorization header for backward compatibility
        $authHeader = $request->getHeaderLine('Authorization');
        if ($authHeader) {
            return $this->extractTokenFromHeader($authHeader);
        }

        return null;
    }

    /**
     * Check if token is expired
     */
    public function isTokenExpired(array $payload): bool
    {
        return isset($payload['exp']) && $payload['exp'] < time();
    }

    /**
     * Refresh token (generate new token for existing user)
     */
    public function refreshToken(string $token): ?string
    {
        $user = $this->getUserFromToken($token);

        if (!$user) {
            return null;
        }

        return $this->generateToken($user);
    }
}
