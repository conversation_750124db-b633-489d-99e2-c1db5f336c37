"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { Button } from "@app/components/ui/button"
import { Avatar, AvatarFallback } from "@app/components/ui/avatar"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Phone, Calendar, MessageSquare, Voicemail, Edit, Save, X, ArrowLeft, Bot, PhoneCall, Loader2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Separator } from "@app/components/ui/separator"
import Link from "next/link"
import { useState, use, useEffect } from "react"
import { apiClient } from "@app/lib/api-client"
import Lead, { LeadMetrics, LeadMetricsResponse, LeadAnalysis, LeadAnalysisResponse, RelatedLeadsResponse } from "@app/types/lead"
import { Call, CallResponse } from "@app/types/call"
import { Appointment, AppointmentResponse } from "@app/types/appointment"
import Conversation, { ConversationResponse } from "@app/types/conversation";

interface LeadDetailsPageProps {
    params: Promise<{
        id: string
    }>;
}

export default function LeadDetailsPage(props: LeadDetailsPageProps) {
    const params = use(props.params);
    const [isEditing, setIsEditing] = useState(false)

    // Loading states
    const [leadLoading, setLeadLoading] = useState(true)
    const [metricsLoading, setMetricsLoading] = useState(true)
    const [analysisLoading, setAnalysisLoading] = useState(true)
    const [conversationsLoading, setConversationsLoading] = useState(true)
    const [callsLoading, setCallsLoading] = useState(true)
    const [appointmentsLoading, setAppointmentsLoading] = useState(true)
    const [relatedLeadsLoading, setRelatedLeadsLoading] = useState(true)

    // Data states
    const [lead, setLead] = useState<Lead | null>(null)
    const [metrics, setMetrics] = useState<LeadMetrics | null>(null)
    const [analysis, setAnalysis] = useState<LeadAnalysis | null>(null)
    const [conversations, setConversations] = useState<Conversation[]>([])
    const [calls, setCalls] = useState<Call[]>([])
    const [appointments, setAppointments] = useState<Appointment[]>([])
    const [relatedLeads, setRelatedLeads] = useState<Lead[]>([])

    // Error states
    const [leadError, setLeadError] = useState<string | null>(null)
    const [metricsError, setMetricsError] = useState<string | null>(null)
    const [analysisError, setAnalysisError] = useState<string | null>(null)
    const [conversationsError, setConversationsError] = useState<string | null>(null)
    const [callsError, setCallsError] = useState<string | null>(null)
    const [appointmentsError, setAppointmentsError] = useState<string | null>(null)
    const [relatedLeadsError, setRelatedLeadsError] = useState<string | null>(null)

    const [editedLead, setEditedLead] = useState({
        name: "",
        email: "",
        notes: "",
        status: "",
        priority: "",
        address: "",
    })

    // Fetch lead details
    const fetchLead = async () => {
        setLeadLoading(true)
        setLeadError(null)
        try {
            const response = await apiClient.get(`/api/v1/lead/${params.id}`)
            setLead(response)
            setEditedLead({
                name: response.person?.name?.full || "",
                email: response.person?.email || "",
                notes: response.notes?.join('\n') || "",
                status: response.status || "",
                priority: response.priority || "",
                address: "", // Add address field to Lead type if needed
            })
        } catch (err) {
            setLeadError(err instanceof Error ? err.message : 'Failed to fetch lead')
        } finally {
            setLeadLoading(false)
        }
    }

    // Fetch lead metrics
    const fetchMetrics = async () => {
        setMetricsLoading(true)
        setMetricsError(null)
        try {
            const response: LeadMetricsResponse = await apiClient.get(`/api/v1/lead/${params.id}/metrics`)
            setMetrics(response.data)
        } catch (err) {
            setMetricsError(err instanceof Error ? err.message : 'Failed to fetch metrics')
        } finally {
            setMetricsLoading(false)
        }
    }

    // Fetch AI analysis
    const fetchAnalysis = async () => {
        setAnalysisLoading(true)
        setAnalysisError(null)
        try {
            const response: LeadAnalysisResponse = await apiClient.get(`/api/v1/lead/${params.id}/analysis`)
            setAnalysis(response.data)
        } catch (err) {
            setAnalysisError(err instanceof Error ? err.message : 'Failed to fetch analysis')
        } finally {
            setAnalysisLoading(false)
        }
    }

    // Fetch conversations
    const fetchConversations = async () => {
        setConversationsLoading(true)
        setConversationsError(null)
        try {
            const response: ConversationResponse = await apiClient.get(`/api/v1/conversation?filter[lead]=${params.id}`)
            setConversations(response.data)
        } catch (err) {
            setConversationsError(err instanceof Error ? err.message : 'Failed to fetch conversations')
        } finally {
            setConversationsLoading(false)
        }
    }

    // Fetch calls
    const fetchCalls = async () => {
        setCallsLoading(true)
        setCallsError(null)
        try {
            const response: CallResponse = await apiClient.get(`/api/v1/call?filter[lead]=${params.id}`)
            setCalls(response.data)
        } catch (err) {
            setCallsError(err instanceof Error ? err.message : 'Failed to fetch calls')
        } finally {
            setCallsLoading(false)
        }
    }

    // Fetch appointments
    const fetchAppointments = async () => {
        setAppointmentsLoading(true)
        setAppointmentsError(null)
        try {
            const response: AppointmentResponse = await apiClient.get(`/api/v1/appointment?filter[lead]=${params.id}`)
            setAppointments(response.data)
        } catch (err) {
            setAppointmentsError(err instanceof Error ? err.message : 'Failed to fetch appointments')
        } finally {
            setAppointmentsLoading(false)
        }
    }

    // Fetch related leads
    const fetchRelatedLeads = async () => {
        setRelatedLeadsLoading(true)
        setRelatedLeadsError(null)
        try {
            const response: RelatedLeadsResponse = await apiClient.get(`/api/v1/lead/${params.id}/related`)
            setRelatedLeads(response.data)
        } catch (err) {
            setRelatedLeadsError(err instanceof Error ? err.message : 'Failed to fetch related leads')
        } finally {
            setRelatedLeadsLoading(false)
        }
    }

    // Load all data on component mount
    useEffect(() => {
        fetchLead()
        fetchMetrics()
        fetchAnalysis()
        fetchConversations()
        fetchCalls()
        fetchAppointments()
        fetchRelatedLeads()
    }, [params.id])

    // Show loading state if lead is not loaded yet
    if (leadLoading || !lead) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Loader2 className="h-8 w-8 animate-spin" />
            </div>
        )
    }

    // Show error state if lead failed to load
    if (leadError) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-red-600 mb-2">Error Loading Lead</h2>
                    <p className="text-gray-600 mb-4">{leadError}</p>
                    <Button onClick={fetchLead}>Try Again</Button>
                </div>
            </div>
        )
    }



    const handleSave = async () => {
        try {
            // TODO: Implement lead update API call
            console.log("Saving lead updates:", editedLead)
            setIsEditing(false)
            // Refresh lead data after save
            await fetchLead()
        } catch (err) {
            console.error("Failed to save lead:", err)
        }
    }

    const handleCancel = () => {
        // Reset to original values
        if (lead) {
            setEditedLead({
                name: lead.person?.name?.full || "",
                email: lead.person?.email || "",
                notes: lead.notes?.join('\n') || "",
                status: lead.status || "",
                priority: lead.priority || "",
                address: "", // Add address field to Lead type if needed
            })
        }
        setIsEditing(false)
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case "new":
                return "default"
            case "contacted":
                return "secondary"
            case "qualified":
                return "outline"
            case "converted":
                return "default"
            case "lost":
                return "destructive"
            default:
                return "secondary"
        }
    }

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case "high":
                return "text-red-500"
            case "medium":
                return "text-yellow-500"
            case "low":
                return "text-green-500"
            default:
                return "text-gray-500"
        }
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Link href="/leads">
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Leads
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{lead.person?.name?.full || 'Unknown Name'}</h1>
                        <p className="text-muted-foreground">
                            {lead.service || 'No service specified'} • {lead.phone || 'No phone'}
                        </p>
                    </div>
                </div>
                <div className="flex gap-2">
                    {isEditing ? (
                        <>
                            <Button variant="outline" onClick={handleCancel}>
                                <X className="mr-2 h-4 w-4" />
                                Cancel
                            </Button>
                            <Button onClick={handleSave}>
                                <Save className="mr-2 h-4 w-4" />
                                Save Changes
                            </Button>
                        </>
                    ) : (
                        <Button onClick={() => setIsEditing(true)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Lead
                        </Button>
                    )}
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Lead Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Lead Information</CardTitle>
                            <CardDescription>Contact details and current status</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Name</Label>
                                    {isEditing ? (
                                        <Input
                                            id="name"
                                            value={editedLead.name}
                                            onChange={(e) => setEditedLead({ ...editedLead, name: e.target.value })}
                                        />
                                    ) : (
                                        <p className="text-sm font-medium">{lead.person?.name?.full || 'Unknown Name'}</p>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email</Label>
                                    {isEditing ? (
                                        <Input
                                            id="email"
                                            type="email"
                                            value={editedLead.email}
                                            onChange={(e) => setEditedLead({ ...editedLead, email: e.target.value })}
                                        />
                                    ) : (
                                        <p className="text-sm">{lead.person?.email || 'No email'}</p>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label>Phone</Label>
                                    <p className="text-sm">{lead.phone || 'No phone'}</p>
                                </div>
                                <div className="space-y-2">
                                    <Label>Service Requested</Label>
                                    <p className="text-sm">{lead.service || 'No service specified'}</p>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="status">Status</Label>
                                    {isEditing ? (
                                        <Select
                                            value={editedLead.status}
                                            onValueChange={(value) => setEditedLead({ ...editedLead, status: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="new">New</SelectItem>
                                                <SelectItem value="contacted">Contacted</SelectItem>
                                                <SelectItem value="qualified">Qualified</SelectItem>
                                                <SelectItem value="converted">Converted</SelectItem>
                                                <SelectItem value="lost">Lost</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <Badge variant={getStatusColor(lead.status)} className="capitalize w-fit">
                                            {lead.status}
                                        </Badge>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="priority">Priority</Label>
                                    {isEditing ? (
                                        <Select
                                            value={editedLead.priority}
                                            onValueChange={(value) => setEditedLead({ ...editedLead, priority: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="high">High</SelectItem>
                                                <SelectItem value="medium">Medium</SelectItem>
                                                <SelectItem value="low">Low</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <span className={`text-sm font-medium ${getPriorityColor(lead.priority)} capitalize`}>
                      {lead.priority}
                    </span>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="address">Address</Label>
                                {isEditing ? (
                                    <Input
                                        id="address"
                                        value={editedLead.address}
                                        onChange={(e) => setEditedLead({ ...editedLead, address: e.target.value })}
                                    />
                                ) : (
                                    <p className="text-sm">{lead.address}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes</Label>
                                {isEditing ? (
                                    <Textarea
                                        id="notes"
                                        value={editedLead.notes}
                                        onChange={(e) => setEditedLead({ ...editedLead, notes: e.target.value })}
                                        rows={3}
                                    />
                                ) : (
                                    <p className="text-sm">{lead.notes}</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* AI Analysis */}
                    <Card>
                        <CardHeader>
                            <CardTitle>AI Analysis</CardTitle>
                            <CardDescription>Automated insights and sentiment analysis</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {analysisLoading ? (
                                <div className="flex items-center justify-center py-8">
                                    <Loader2 className="h-6 w-6 animate-spin" />
                                    <span className="ml-2">Loading AI analysis...</span>
                                </div>
                            ) : analysisError ? (
                                <div className="text-center py-8">
                                    <p className="text-red-600 mb-2">Failed to load AI analysis</p>
                                    <Button variant="outline" size="sm" onClick={fetchAnalysis}>
                                        Retry
                                    </Button>
                                </div>
                            ) : analysis ? (
                                <>
                                    <div>
                                        <Label>Lead Summary</Label>
                                        <p className="text-sm mt-1">{analysis.summary}</p>
                                    </div>
                                    <div>
                                        <Label>Sentiment Analysis</Label>
                                        <p className="text-sm mt-1">{analysis.sentiment}</p>
                                    </div>
                                    <div>
                                        <Label>Confidence Score</Label>
                                        <p className="text-sm mt-1">{analysis.confidence}%</p>
                                    </div>
                                    <div>
                                        <Label>Key Insights</Label>
                                        <ul className="text-sm mt-1 list-disc list-inside space-y-1">
                                            {analysis.keyInsights.map((insight, index) => (
                                                <li key={index}>{insight}</li>
                                            ))}
                                        </ul>
                                    </div>
                                </>
                            ) : (
                                <p className="text-sm text-muted-foreground">No analysis available</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Recent Conversations */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Conversations</CardTitle>
                            <CardDescription>Latest interactions with this lead</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {conversationsLoading ? (
                                <div className="flex items-center justify-center py-8">
                                    <Loader2 className="h-6 w-6 animate-spin" />
                                    <span className="ml-2">Loading conversations...</span>
                                </div>
                            ) : conversationsError ? (
                                <div className="text-center py-8">
                                    <p className="text-red-600 mb-2">Failed to load conversations</p>
                                    <Button variant="outline" size="sm" onClick={fetchConversations}>
                                        Retry
                                    </Button>
                                </div>
                            ) : conversations.length > 0 ? (
                                <div className="space-y-4">
                                    {conversations.map((conversation) => (
                                        <div key={conversation.id} className="flex items-start gap-3 p-3 border rounded-lg">
                                            <Avatar className="h-8 w-8">
                                                <AvatarFallback>
                                                    <Bot className="h-4 w-4" />
                                                </AvatarFallback>
                                            </Avatar>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <p className="text-sm font-medium">{conversation.agent.name}</p>
                                                    <Badge variant={conversation.status === "active" ? "default" : "secondary"} className="text-xs">
                                                        {conversation.status}
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground mt-1">{conversation.lastMessage}</p>
                                                <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                                    <span>{conversation.messageCount} messages</span>
                                                    <span>{new Date(conversation.lastMessageTime).toLocaleString()}</span>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-muted-foreground text-center py-8">No conversations found</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Calls & Voicemails */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Call History</CardTitle>
                            <CardDescription>Phone interactions with this lead</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {callsLoading ? (
                                <div className="flex items-center justify-center py-8">
                                    <Loader2 className="h-6 w-6 animate-spin" />
                                    <span className="ml-2">Loading call history...</span>
                                </div>
                            ) : callsError ? (
                                <div className="text-center py-8">
                                    <p className="text-red-600 mb-2">Failed to load call history</p>
                                    <Button variant="outline" size="sm" onClick={fetchCalls}>
                                        Retry
                                    </Button>
                                </div>
                            ) : calls.length > 0 ? (
                                <div className="space-y-4">
                                    {calls.map((call) => (
                                        <div key={call.id} className="flex items-start gap-3 p-3 border rounded-lg">
                                            <div
                                                className={`p-2 rounded-full ${call.answered ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"}`}
                                            >
                                                <PhoneCall className="h-4 w-4" />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <p className="text-sm font-medium">{call.answered ? 'Answered' : 'Missed'} Call</p>
                                                    <span className="text-xs text-muted-foreground">{Math.floor(call.duration / 60)}:{(call.duration % 60).toString().padStart(2, '0')}</span>
                                                </div>
                                                <p className="text-sm text-muted-foreground">{call.agent?.name || 'Unknown Agent'}</p>
                                                <p className="text-sm mt-1">Status: {call.status}</p>
                                                {call.voicemail && (
                                                    <p className="text-sm text-blue-600 mt-1">Voicemail: {call.voicemail.transcription}</p>
                                                )}
                                                <p className="text-xs text-muted-foreground mt-1">{new Date(call.startTime).toLocaleString()}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-muted-foreground text-center py-8">No call history found</p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    {/* Metrics */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Lead Metrics</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {metricsLoading ? (
                                <div className="flex items-center justify-center py-8">
                                    <Loader2 className="h-6 w-6 animate-spin" />
                                    <span className="ml-2">Loading metrics...</span>
                                </div>
                            ) : metricsError ? (
                                <div className="text-center py-8">
                                    <p className="text-red-600 mb-2">Failed to load metrics</p>
                                    <Button variant="outline" size="sm" onClick={fetchMetrics}>
                                        Retry
                                    </Button>
                                </div>
                            ) : metrics ? (
                                <>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="text-center">
                                            <MessageSquare className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                            <p className="text-2xl font-bold">{metrics.messageCount}</p>
                                            <p className="text-xs text-muted-foreground">Messages</p>
                                        </div>
                                        <div className="text-center">
                                            <Phone className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                            <p className="text-2xl font-bold">{metrics.callCount}</p>
                                            <p className="text-xs text-muted-foreground">Calls</p>
                                        </div>
                                        <div className="text-center">
                                            <Voicemail className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                            <p className="text-2xl font-bold">{metrics.voicemailCount}</p>
                                            <p className="text-xs text-muted-foreground">Voicemails</p>
                                        </div>
                                        <div className="text-center">
                                            <Calendar className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                            <p className="text-2xl font-bold">{metrics.appointmentCount}</p>
                                            <p className="text-xs text-muted-foreground">Appointments</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="space-y-3">
                                        <div className="flex justify-between">
                                            <span className="text-sm text-muted-foreground">Lead Score</span>
                                            <span className="text-sm font-medium">{metrics.leadScore}/100</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-sm text-muted-foreground">Estimated Value</span>
                                            <span className="text-sm font-medium">${lead.estimatedValue || 0}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-sm text-muted-foreground">Cost</span>
                                            <span className="text-sm font-medium">{metrics.totalCost}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-sm text-muted-foreground">Avg Response</span>
                                            <span className="text-sm font-medium">{metrics.averageResponseTime}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-sm text-muted-foreground">Last Contact</span>
                                            <span className="text-sm font-medium">{metrics.lastContact ? new Date(metrics.lastContact).toLocaleDateString() : 'Never'}</span>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <p className="text-sm text-muted-foreground text-center py-8">No metrics available</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Appointments */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Appointments</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {appointmentsLoading ? (
                                <div className="flex items-center justify-center py-8">
                                    <Loader2 className="h-6 w-6 animate-spin" />
                                    <span className="ml-2">Loading appointments...</span>
                                </div>
                            ) : appointmentsError ? (
                                <div className="text-center py-8">
                                    <p className="text-red-600 mb-2">Failed to load appointments</p>
                                    <Button variant="outline" size="sm" onClick={fetchAppointments}>
                                        Retry
                                    </Button>
                                </div>
                            ) : appointments.length > 0 ? (
                                <div className="space-y-3">
                                    {appointments.map((appointment) => (
                                        <div key={appointment.id} className="p-3 border rounded-lg">
                                            <div className="flex items-center justify-between mb-2">
                                                <p className="text-sm font-medium">{appointment.service}</p>
                                                <Badge variant="outline" className="text-xs">
                                                    {appointment.status}
                                                </Badge>
                                            </div>
                                            <p className="text-xs text-muted-foreground">{appointment.agent?.name || 'Unknown Agent'}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {new Date(appointment.startTime).toLocaleString()} • ${appointment.value}
                                            </p>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-muted-foreground text-center py-8">No appointments scheduled</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Associated Agent */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Associated Agent</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {lead.agent ? (
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p className="text-sm font-medium">{lead.agent.name}</p>
                                            {lead.agent.phone && (
                                                <p className="text-xs text-muted-foreground">{lead.agent.phone}</p>
                                            )}
                                        </div>
                                        <Badge variant="secondary" className="text-xs">
                                            {lead.agent.metrics?.interactionsWithThisLead || 0} interactions
                                        </Badge>
                                    </div>

                                    {lead.agent.metrics && (
                                        <div className="grid grid-cols-2 gap-3 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Total Leads</span>
                                                <span className="font-medium">{lead.agent.metrics.totalLeads}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">This Month</span>
                                                <span className="font-medium">{lead.agent.metrics.leadsThisMonth}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Conversations</span>
                                                <span className="font-medium">{lead.agent.metrics.totalConversations}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Calls</span>
                                                <span className="font-medium">{lead.agent.metrics.totalCalls}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Conversion Rate</span>
                                                <span className="font-medium">{lead.agent.metrics.conversionRate}%</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Avg Response</span>
                                                <span className="font-medium">{lead.agent.metrics.averageResponseTime}</span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <p className="text-sm text-muted-foreground text-center py-8">No associated agent</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Related Leads */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Related Leads</CardTitle>
                            <CardDescription>Other leads from the same person</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {relatedLeadsLoading ? (
                                <div className="flex items-center justify-center py-8">
                                    <Loader2 className="h-6 w-6 animate-spin" />
                                    <span className="ml-2">Loading related leads...</span>
                                </div>
                            ) : relatedLeadsError ? (
                                <div className="text-center py-8">
                                    <p className="text-red-600 mb-2">Failed to load related leads</p>
                                    <Button variant="outline" size="sm" onClick={fetchRelatedLeads}>
                                        Retry
                                    </Button>
                                </div>
                            ) : relatedLeads.length > 0 ? (
                                <div className="space-y-3">
                                    {relatedLeads.map((relatedLead) => (
                                        <Link key={relatedLead.id} href={`/leads/${relatedLead.id}`}>
                                            <div className="p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                                                <div className="flex items-center justify-between mb-2">
                                                    <p className="text-sm font-medium">{relatedLead.service || 'No service specified'}</p>
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant="outline" className="text-xs">
                                                            {relatedLead.status}
                                                        </Badge>
                                                        <Badge variant="secondary" className="text-xs">
                                                            {relatedLead.priority}
                                                        </Badge>
                                                    </div>
                                                </div>
                                                <p className="text-xs text-muted-foreground">
                                                    Agent: {relatedLead.agent?.name || 'No agent'} •
                                                    Created: {new Date(relatedLead.metadata.createdAt).toLocaleDateString()}
                                                </p>
                                                {relatedLead.estimatedValue && (
                                                    <p className="text-xs text-muted-foreground mt-1">
                                                        Value: ${relatedLead.estimatedValue}
                                                    </p>
                                                )}
                                            </div>
                                        </Link>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-muted-foreground text-center py-8">No other leads from this person</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* System Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle>System Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3 text-sm">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Created</span>
                                <span>{lead.metadata?.createdAt ? new Date(lead.metadata.createdAt).toLocaleString() : 'Unknown'}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Updated</span>
                                <span>{lead.metadata?.updatedAt ? new Date(lead.metadata.updatedAt).toLocaleString() : 'Unknown'}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Updated By</span>
                                <span>{lead.metadata?.updatedBy || 'Unknown'}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
