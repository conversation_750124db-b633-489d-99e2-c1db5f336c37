"use client";

import { useEffect, createContext, useContext, useState, ReactNode } from 'react';
import { apiClient } from './api-client';

// Context to coordinate token handling
interface TokenHandlerContextType {
  isTokenHandled: boolean;
  hasUrlToken: boolean;
}

const TokenHandlerContext = createContext<TokenHandlerContextType>({
  isTokenHandled: false,
  hasUrlToken: false,
});

export const useTokenHandlerContext = () => useContext(TokenHandlerContext);

/**
 * TokenHandlerProvider that coordinates token extraction and auth validation
 * This ensures token handling completes before auth validation runs
 */
export function TokenHandlerProvider({ children }: { children: ReactNode }) {
  const [isTokenHandled, setIsTokenHandled] = useState(false);
  const [hasUrlToken, setHasUrlToken] = useState(false);

  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined') {
      setIsTokenHandled(true);
      return;
    }

    // Check for token in URL parameters (from login redirect)
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');

    if (urlToken) {
      console.log('Token received from login redirect');
      setHasUrlToken(true);

      // Store the token securely
      apiClient.setToken(urlToken);

      // Clean up the URL by removing the token parameter
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('token');

      // Replace the current URL without the token (for security)
      window.history.replaceState({}, '', newUrl.toString());

      console.log('Login successful - token stored securely');
    }

    // Mark token handling as complete
    setIsTokenHandled(true);
  }, []);

  const contextValue: TokenHandlerContextType = {
    isTokenHandled,
    hasUrlToken,
  };

  return (
    <TokenHandlerContext.Provider value={contextValue}>
      {children}
    </TokenHandlerContext.Provider>
  );
}

/**
 * Legacy TokenHandler component for backward compatibility
 * @deprecated Use TokenHandlerProvider instead
 */
export function TokenHandler() {
  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined') return;

    // Check for token in URL parameters (from login redirect)
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');

    if (urlToken) {
      console.log('Token received from login redirect');

      // Store the token
      apiClient.setToken(urlToken);

      // Clean up the URL by removing the token parameter
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('token');

      // Replace the current URL without the token (for security)
      window.history.replaceState({}, '', newUrl.toString());

      // Optional: Show a brief success message
      console.log('Login successful - token stored');
    }
  }, []);

  // This component doesn't render anything
  return null;
}

/**
 * Hook to handle token from URL parameters
 * Alternative to the component approach
 */
export function useTokenFromUrl() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');

    if (urlToken) {
      apiClient.setToken(urlToken);
      
      // Clean up URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('token');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, []);
}
