'use client';

export type UnauthorizedHandler = () => void;

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;
  private onUnauthorized?: UnauthorizedHandler;

  // Cookie name for secure cross-subdomain storage
  private static COOKIE_NAME = 'bt_auth_token';
  // Fallback localStorage key
  private static STORAGE_KEY = 'bt_auth_token';

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/+$/, '');
    console.log(process.env.BACK_TALK_API_ENDPOINT);
    if (typeof window !== 'undefined') {
      // Try to bootstrap from secure cookie first, then localStorage
      const cookieToken = this.getTokenFromCookie();
      const storageToken = window.localStorage.getItem(ApiClient.STORAGE_KEY);

      if (cookieToken) {
        this.token = cookieToken;
      } else if (storageToken) {
        this.token = storageToken;
        // Migrate to cookie for better security
        this.setTokenCookie(storageToken);
      }
    }
  }

  /** Get token from secure cookie */
  private getTokenFromCookie(): string | null {
    if (typeof window === 'undefined') return null;

    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === ApiClient.COOKIE_NAME) {
        return decodeURIComponent(value);
      }
    }
    return null;
  }

  /** Set token in secure cookie with cross-subdomain support */
  private setTokenCookie(token: string): void {
    if (typeof window === 'undefined') return;

    const domain = window.location.hostname.includes('localhost')
      ? 'localhost'
      : '.back-talk.ai'; // Cross-subdomain support

    const cookieOptions = [
      `${ApiClient.COOKIE_NAME}=${encodeURIComponent(token)}`,
      `Domain=${domain}`,
      'Path=/',
      'Secure', // Only over HTTPS
      'SameSite=Strict', // CSRF protection
      `Max-Age=${7 * 24 * 60 * 60}` // 7 days
    ];

    document.cookie = cookieOptions.join('; ');
  }

  /** Remove token cookie */
  private removeTokenCookie(): void {
    if (typeof window === 'undefined') return;

    const domain = window.location.hostname.includes('localhost')
      ? 'localhost'
      : '.back-talk.ai';

    document.cookie = `${ApiClient.COOKIE_NAME}=; Domain=${domain}; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  }

  /** Called by TokenHandler (and by use-auth on logout) */
  setToken(token: string | null, { persist = true }: { persist?: boolean } = {}) {
    this.token = token;
    if (typeof window !== 'undefined') {
      if (persist && token) {
        // Store in both cookie (primary) and localStorage (fallback)
        this.setTokenCookie(token);
        window.localStorage.setItem(ApiClient.STORAGE_KEY, token);
      } else {
        // Remove from both storage methods
        this.removeTokenCookie();
        window.localStorage.removeItem(ApiClient.STORAGE_KEY);
      }
    }
  }

  /** Read current token (used by use-auth) */
  getToken(): string | null {
    return this.token;
  }

  /** Optional: allow use-auth to plug in a global 401 handler */
  setOnUnauthorized(handler?: UnauthorizedHandler) {
    this.onUnauthorized = handler;
  }

  private withAuth(headers: HeadersInit = {}): HeadersInit {
    const h: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(headers as Record<string, string>),
    };
    if (this.token && !('Authorization' in h)) {
      h.Authorization = `Bearer ${this.token}`;
    }
    return h;
  }

  async request<T = unknown>(endpoint: string, init: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = this.withAuth(init.headers || {});
    const config: RequestInit = { ...init, headers };

    const res = await fetch(url, config);

    if (res.status === 401) {
      // Give the app a chance to clear state/redirect
      this.onUnauthorized?.();
      throw new Error('Authentication required');
    }

    if (!res.ok) {
      // Try to parse error JSON; fall back to status text
      let message = res.statusText;
      try {
        const data = await res.json();
        message = data?.message ?? message;
      } catch {
        /* ignore */
      }
      throw new Error(message || `HTTP error ${res.status}`);
    }

    if (res.status === 204) {
      // no content
      return undefined as T;
    }

    // Assume JSON by default
    return (await res.json()) as T;
  }

  get<T = unknown>(endpoint: string, init?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...init, method: 'GET' });
  }

  post<T = unknown, B = unknown>(endpoint: string, body?: B, init?: RequestInit) {
    return this.request<T>(endpoint, {
      ...init,
      method: 'POST',
      body: body === undefined ? undefined : JSON.stringify(body),
    });
  }

  put<T = unknown, B = unknown>(endpoint: string, body?: B, init?: RequestInit) {
    return this.request<T>(endpoint, {
      ...init,
      method: 'PUT',
      body: body === undefined ? undefined : JSON.stringify(body),
    });
  }

  patch<T = unknown, B = unknown>(endpoint: string, body?: B, init?: RequestInit) {
    return this.request<T>(endpoint, {
      ...init,
      method: 'PATCH',
      body: body === undefined ? undefined : JSON.stringify(body),
    });
  }

  delete<T = unknown>(endpoint: string, init?: RequestInit) {
    return this.request<T>(endpoint, { ...init, method: 'DELETE' });
  }
}

// Export a singleton configured from env
export const apiClient = new ApiClient(process.env.NEXT_PUBLIC_BACK_TALK_API_ENDPOINT ?? '');
