'use client';

export type UnauthorizedHandler = () => void;

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;
  private onUnauthorized?: UnauthorizedHandler;

  // Optional: persist token across reloads
  private static STORAGE_KEY = 'bt_auth_token';

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/+$/, '');
    console.log(process.env.BACK_TALK_API_ENDPOINT);
    if (typeof window !== 'undefined') {
      // bootstrap from storage if present (To<PERSON><PERSON><PERSON><PERSON> may overwrite shortly)
      const stored = window.localStorage.getItem(ApiClient.STORAGE_KEY);
      if (stored) this.token = stored;
    }
  }

  /** Called by TokenHandler (and by use-auth on logout) */
  setToken(token: string | null, { persist = true }: { persist?: boolean } = {}) {
    this.token = token;
    if (typeof window !== 'undefined') {
      if (persist && token) {
        window.localStorage.setItem(ApiClient.STORAGE_KEY, token);
      } else {
        window.localStorage.removeItem(ApiClient.STORAGE_KEY);
      }
    }
  }

  /** Read current token (used by use-auth) */
  getToken(): string | null {
    return this.token;
  }

  /** Optional: allow use-auth to plug in a global 401 handler */
  setOnUnauthorized(handler?: UnauthorizedHandler) {
    this.onUnauthorized = handler;
  }

  private withAuth(headers: HeadersInit = {}): HeadersInit {
    const h: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(headers as Record<string, string>),
    };
    if (this.token && !('Authorization' in h)) {
      h.Authorization = `Bearer ${this.token}`;
    }
    return h;
  }

  async request<T = unknown>(endpoint: string, init: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = this.withAuth(init.headers || {});
    const config: RequestInit = { ...init, headers };

    const res = await fetch(url, config);

    if (res.status === 401) {
      // Give the app a chance to clear state/redirect
      this.onUnauthorized?.();
      throw new Error('Authentication required');
    }

    if (!res.ok) {
      // Try to parse error JSON; fall back to status text
      let message = res.statusText;
      try {
        const data = await res.json();
        message = data?.message ?? message;
      } catch {
        /* ignore */
      }
      throw new Error(message || `HTTP error ${res.status}`);
    }

    if (res.status === 204) {
      // no content
      return undefined as T;
    }

    // Assume JSON by default
    return (await res.json()) as T;
  }

  get<T = unknown>(endpoint: string, init?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...init, method: 'GET' });
  }

  post<T = unknown, B = unknown>(endpoint: string, body?: B, init?: RequestInit) {
    return this.request<T>(endpoint, {
      ...init,
      method: 'POST',
      body: body === undefined ? undefined : JSON.stringify(body),
    });
  }

  put<T = unknown, B = unknown>(endpoint: string, body?: B, init?: RequestInit) {
    return this.request<T>(endpoint, {
      ...init,
      method: 'PUT',
      body: body === undefined ? undefined : JSON.stringify(body),
    });
  }

  patch<T = unknown, B = unknown>(endpoint: string, body?: B, init?: RequestInit) {
    return this.request<T>(endpoint, {
      ...init,
      method: 'PATCH',
      body: body === undefined ? undefined : JSON.stringify(body),
    });
  }

  delete<T = unknown>(endpoint: string, init?: RequestInit) {
    return this.request<T>(endpoint, { ...init, method: 'DELETE' });
  }
}

// Export a singleton configured from env
export const apiClient = new ApiClient(process.env.NEXT_PUBLIC_BACK_TALK_API_ENDPOINT ?? '');
