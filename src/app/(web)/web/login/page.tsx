"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import Logo from "@/assets/logo.svg";
import {NextResponse} from "next/server";

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Call the backend API to authenticate
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.back-talk.ai'
      const response = await fetch(`${apiUrl}/auth/login_check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: email,
          password: password,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Login failed')
      }

      const data = await response.json()

      if (data.success && data.token) {
        // Store token in secure cookie for cross-subdomain access
        const domain = window.location.hostname.includes('localhost')
          ? 'localhost'
          : '.back-talk.ai';

        const cookieOptions = [
          `bt_auth_token=${encodeURIComponent(data.token)}`,
          `Domain=${domain}`,
          'Path=/',
          'Secure',
          'SameSite=Strict',
          `Max-Age=${7 * 24 * 60 * 60}` // 7 days
        ];

        document.cookie = cookieOptions.join('; ');

        // Determine the app URL based on current environment
        let appUrl: URL

        if (window.location.hostname === 'localhost') {
          // Development environment
          appUrl = new URL(`http://app.localhost:${window.location.port || '9000'}`)
        } else {
          // Production environment
          appUrl = new URL("https://app.back-talk.ai")
        }

        // Redirect without token in URL for better security
        window.location.replace(appUrl.toString())
      } else {
        throw new Error('Invalid response from server')
      }
    } catch (error) {
      console.error('Login error:', error)
      alert(error instanceof Error ? error.message : 'Login failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
        <div className="min-h-screen flex items-center justify-center px-4 z-10">
          <div className="w-full max-w-md">
            {/* Logo and Header */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <Logo aria-label="Back-Talk logo" width={40} height={40} style={{ "--logo-primary": "white", "--logo-secondary": "var(--color-primary-300)" }} className="w-10 h-10" />
                <span className="text-white text-2xl font-bold">back-talk</span>
              </div>
              <h1 className="text-3xl font-bold text-white mb-2">Welcome Back</h1>
              <p className="text-neutral-300">Sign in to your account</p>
            </div>

            {/* Login Form */}
            <div className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-2xl p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                    Email Address
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                    placeholder="Enter your email"
                  />
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                    Password
                  </label>
                  <input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                    placeholder="Enter your password"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="w-4 h-4 text-primary-500 bg-white/10 border-white/20 rounded focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-neutral-300">Remember me</span>
                  </label>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-primary-300 hover:text-primary-200 transition-colors"
                  >
                    Forgot password?
                  </Link>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-800 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-black"
                  onClick={handleSubmit}
                >
                  {isLoading ? "Signing in..." : "Sign In"}
                </button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-neutral-300">
                  Don't have an account?{" "}
                  <Link
                    href="/signup"
                    className="text-primary-300 hover:text-primary-200 font-medium transition-colors"
                  >
                    Sign up
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
  )
}
